import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import urllib.robotparser
from urllib.parse import urlparse, urljoin
import csv
import json
import logging
import re
import datetime
import argparse
from dateutil import parser as date_parser
from requests.exceptions import RequestException, Timeout, ConnectionError

# Keywords for filtering technical content
INCLUDE_KEYWORDS = [
    # AI technologies
    "ai", "artificial intelligence", "machine learning", "deep learning", "neural network",
    # AI models and applications
    "gpt", "llm", "large language model", "chatbot", "generative ai",
    # Technical domains
    "robotics", "automation", "nlp", "natural language processing", "blockchain", "quantum computing",
    # Research and innovation terms
    "innovation", "research", "breakthrough", "algorithm", "model", "framework",
    # Implementation terms
    "launch", "platform", "deployment", "api", "infrastructure", "open source"
]

# Keywords for filtering out non-technical content
EXCLUDE_KEYWORDS = [
    # Business drama
    "lawsuit", "litigation", "scandal", "controversy", "legal battle",
    # Corporate news
    "ceo", "executive", "resign", "layoffs", "fired", "stock price", "valuation",
    # Social media drama
    "twitter", "x", "elon musk", "meta", "zuckerberg",
    # Speculative content
    "rumor", "leak", "allegedly", "sources say", "reportedly",
    # Generic business news
    "funding", "acquisition", "merger", "investor", "quarterly results"
]

def is_relevant_article(article, strict=False):
    """
    Determine if an article is relevant based on keyword matching.

    Args:
        article (dict): Article dictionary containing title and other fields
        strict (bool): If True, requires multiple include keywords to match

    Returns:
        bool: True if the article is relevant, False otherwise
    """
    # Extract the title and convert to lowercase for case-insensitive matching
    title = article.get('title', '').lower()

    # Skip empty titles or placeholder titles
    if not title or title == "no title found" or title == "most popular":
        return False

    # Debug logging for strict mode
    if strict:
        logger.debug(f"Checking article with strict mode: {title}")

    # Check for include keywords with word boundary handling
    include_matches = []
    for keyword in INCLUDE_KEYWORDS:
        # Use word boundary pattern to avoid partial matches
        # e.g., "ai" should match "ai" but not "paid" or "main"
        pattern = r'\b' + re.escape(keyword.lower()) + r'\b'
        if re.search(pattern, title):
            include_matches.append(keyword)

    # Check for exclude keywords with word boundary handling
    exclude_match = False
    for keyword in EXCLUDE_KEYWORDS:
        pattern = r'\b' + re.escape(keyword.lower()) + r'\b'
        if re.search(pattern, title):
            exclude_match = True
            break

    # Determine relevance based on matches and strictness
    if strict:
        # Strict mode: require at least 2 include keywords and no exclude keywords
        is_relevant = len(include_matches) >= 2 and not exclude_match
        if is_relevant:
            logger.debug(f"STRICT MATCH: {title} - Keywords: {include_matches}")
        return is_relevant
    else:
        # Normal mode: require at least 1 include keyword and no exclude keywords
        is_relevant = len(include_matches) >= 1 and not exclude_match
        if is_relevant:
            logger.debug(f"NORMAL MATCH: {title} - Keywords: {include_matches}")
        return is_relevant

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("scraper.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WebScraper:
    def __init__(self, delay_min=1, delay_max=3, respect_robots=True, timeout=30,
                 time_window_days=2, include_no_date=True):
        """
        Initialize the web scraper with configurable parameters.

        Args:
            delay_min (int): Minimum delay between requests in seconds
            delay_max (int): Maximum delay between requests in seconds
            respect_robots (bool): Whether to respect robots.txt
            timeout (int): Request timeout in seconds
            time_window_days (int): Number of days to look back for articles (0 for same day only)
            include_no_date (bool): Whether to include articles with no date information
        """
        self.delay_min = delay_min
        self.delay_max = delay_max
        self.respect_robots = respect_robots
        self.timeout = timeout
        self.time_window_days = time_window_days
        self.include_no_date = include_no_date
        self.robots_cache = {}  # Cache for robots.txt parsers

    def _get_robots_parser(self, url):
        """
        Get or create a robots.txt parser for the given URL's domain.

        Args:
            url (str): The URL to check

        Returns:
            urllib.robotparser.RobotFileParser: A parser for the domain's robots.txt
        """
        parsed_url = urlparse(url)
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

        if base_url not in self.robots_cache:
            rp = urllib.robotparser.RobotFileParser()
            rp.set_url(urljoin(base_url, "/robots.txt"))
            try:
                rp.read()
                self.robots_cache[base_url] = rp
            except Exception as e:
                logger.warning(f"Error reading robots.txt for {base_url}: {e}")
                # If we can't read robots.txt, we'll assume it's allowed
                self.robots_cache[base_url] = None

        return self.robots_cache[base_url]

    def _can_fetch(self, url):
        """
        Check if the URL can be fetched according to robots.txt.

        Args:
            url (str): The URL to check

        Returns:
            bool: True if the URL can be fetched, False otherwise
        """
        if not self.respect_robots:
            return True

        rp = self._get_robots_parser(url)
        if rp is None:
            return True

        return rp.can_fetch("*", url)

    def _extract_date(self, article, parser_config=None):
        """
        Extract the publication date from an article element.

        Args:
            article (bs4.element.Tag): The article element
            parser_config (dict, optional): Configuration for the parser

        Returns:
            datetime.datetime or None: The publication date if found, None otherwise
        """
        if parser_config is None or 'date_selector' not in parser_config:
            # Default date selectors
            date_selectors = [
                'time', '.date', '.time', '.datetime', '.published', '.pubdate',
                '[datetime]', '[pubdate]', '.timestamp', '.post-date', '.entry-date',
                'meta[property="article:published_time"]', 'meta[itemprop="datePublished"]'
            ]
        else:
            date_selectors = [parser_config['date_selector']]

        # Try to find date using selectors
        for selector in date_selectors:
            date_elements = article.select(selector)
            if not date_elements and selector.startswith('meta'):
                # Try to find meta tags in the whole document
                date_elements = article.find_all(selector)

            for date_element in date_elements:
                # Check for datetime attribute
                if date_element.has_attr('datetime'):
                    try:
                        return date_parser.parse(date_element['datetime'])
                    except (ValueError, TypeError):
                        pass

                # Check for content attribute (meta tags)
                if date_element.has_attr('content'):
                    try:
                        return date_parser.parse(date_element['content'])
                    except (ValueError, TypeError):
                        pass

                # Try to parse the text content
                try:
                    date_text = date_element.get_text().strip()
                    if date_text:
                        return date_parser.parse(date_text)
                except (ValueError, TypeError):
                    pass

        # Try to find date patterns in the article text
        article_text = article.get_text()

        # Common date patterns
        date_patterns = [
            # ISO format: 2023-05-23
            r'\d{4}-\d{2}-\d{2}',
            # Common formats: May 23, 2023 or 23 May 2023
            r'(?:\d{1,2}\s+)?(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\.?\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4}',
            r'(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\.?\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4}',
            # MM/DD/YYYY or DD/MM/YYYY
            r'\d{1,2}/\d{1,2}/\d{4}',
            # Relative time: "2 hours ago", "yesterday", etc.
            r'(\d+)\s+(second|minute|hour|day|week|month|year)s?\s+ago',
            r'yesterday|today'
        ]

        for pattern in date_patterns:
            match = re.search(pattern, article_text)
            if match:
                try:
                    date_str = match.group(0)

                    # Handle relative time
                    if 'ago' in date_str:
                        num, unit = re.findall(r'(\d+)\s+(second|minute|hour|day|week|month|year)', date_str)[0]
                        num = int(num)
                        now = datetime.datetime.now()

                        if unit == 'second':
                            return now - datetime.timedelta(seconds=num)
                        elif unit == 'minute':
                            return now - datetime.timedelta(minutes=num)
                        elif unit == 'hour':
                            return now - datetime.timedelta(hours=num)
                        elif unit == 'day':
                            return now - datetime.timedelta(days=num)
                        elif unit == 'week':
                            return now - datetime.timedelta(weeks=num)
                        elif unit == 'month':
                            # Approximate a month as 30 days
                            return now - datetime.timedelta(days=30*num)
                        elif unit == 'year':
                            # Approximate a year as 365 days
                            return now - datetime.timedelta(days=365*num)
                    elif date_str == 'yesterday':
                        return datetime.datetime.now() - datetime.timedelta(days=1)
                    elif date_str == 'today':
                        return datetime.datetime.now()
                    else:
                        return date_parser.parse(date_str)
                except (ValueError, TypeError, IndexError):
                    pass

        # If we couldn't find a date, return None
        return None

    def _is_within_time_window(self, article_date):
        """
        Check if an article's publication date is within the specified time window.

        Args:
            article_date (datetime.datetime or None): The article's publication date

        Returns:
            bool: True if the article is within the time window, False otherwise
        """
        if article_date is None:
            return self.include_no_date

        # Make sure we're comparing naive datetimes with naive datetimes
        # or aware datetimes with aware datetimes
        if article_date.tzinfo is not None:
            # Convert to naive datetime by replacing tzinfo with None
            article_date = article_date.replace(tzinfo=None)

        if self.time_window_days == 0:
            # Same day only
            now = datetime.datetime.now()
            return (article_date.year == now.year and
                    article_date.month == now.month and
                    article_date.day == now.day)
        else:
            # Within the specified number of days
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=self.time_window_days)
            return article_date >= cutoff_date

    def fetch_page(self, url):
        """
        Fetch the content of a webpage with error handling.

        Args:
            url (str): The URL to fetch

        Returns:
            requests.Response or None: The response object if successful, None otherwise
        """
        if not self._can_fetch(url):
            logger.warning(f"URL {url} is disallowed by robots.txt")
            return None

        try:
            # Add a random delay to avoid overloading the server
            delay = random.uniform(self.delay_min, self.delay_max)
            time.sleep(delay)

            logger.info(f"Fetching {url}")
            response = requests.get(url, timeout=self.timeout, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            })
            response.raise_for_status()  # Raise an exception for 4XX/5XX responses
            return response
        except Timeout:
            logger.error(f"Timeout error for {url}")
        except ConnectionError:
            logger.error(f"Connection error for {url}")
        except RequestException as e:
            logger.error(f"Error fetching {url}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error fetching {url}: {e}")

        return None

    def parse_articles(self, response, url, parser_config=None):
        """
        Parse the HTML content to extract article information.

        Args:
            response (requests.Response): The response object
            url (str): The URL of the page
            parser_config (dict, optional): Configuration for the parser

        Returns:
            list: A list of dictionaries containing article information
        """
        if response is None:
            return []

        # Default parser configuration
        if parser_config is None:
            parser_config = {
                'article_selector': 'article',
                'title_selector': 'h2 a, h3 a, h1 a, .title a, .headline a',
                'link_selector': 'a',
                'link_attribute': 'href',
                'date_selector': 'time, .date, .time, .datetime, .published, .pubdate, [datetime], [pubdate]'
            }

        try:
            soup = BeautifulSoup(response.content, "html.parser")
            articles = []

            # Try to find articles using the selector
            article_elements = soup.select(parser_config['article_selector'])

            # If no articles found with the selector, try a more generic approach
            if not article_elements:
                logger.info(f"No articles found with selector '{parser_config['article_selector']}', trying generic approach")
                # Look for common article containers
                article_elements = soup.find_all(['article', 'div', 'section'], class_=lambda c: c and any(x in c for x in ['article', 'post', 'entry', 'item', 'jeg_post']))

                # If still no articles found, try to find all divs with links and headers
                if not article_elements:
                    logger.info("Still no articles found, trying to find divs with links and headers")
                    for div in soup.find_all('div'):
                        if div.find(['h1', 'h2', 'h3', 'h4']) and div.find('a'):
                            article_elements.append(div)

            base_url = urlparse(url)
            base_domain = f"{base_url.scheme}://{base_url.netloc}"

            for article in article_elements:
                try:
                    # Try to find the title
                    title_element = article.select_one(parser_config['title_selector'])
                    if not title_element:
                        # Try more generic title selectors
                        title_element = article.find(['h1', 'h2', 'h3', 'h4'])

                    title = title_element.get_text().strip() if title_element else "No title found"

                    # Enhanced URL extraction logic
                    link = None
                    logger.debug(f"Extracting URL for article with title: {title}")

                    # Method 1: Try to get URL from the title element if it's a link
                    if title_element and title_element.name == 'a' and title_element.has_attr(parser_config['link_attribute']):
                        link = title_element[parser_config['link_attribute']]
                        logger.debug(f"Method 1 - URL from title element: {link}")

                    # Method 2: Try to find link using the link selector
                    if not link:
                        link_elements = article.select(parser_config['link_selector'])
                        logger.debug(f"Method 2 - Found {len(link_elements)} link elements using selector: {parser_config['link_selector']}")
                        for link_element in link_elements:
                            if link_element.has_attr(parser_config['link_attribute']):
                                link = link_element[parser_config['link_attribute']]
                                logger.debug(f"Method 2 - URL from link selector: {link}")
                                break

                    # Method 3: Look for any link in the article that might be the main link
                    if not link:
                        # First try links that contain the article title text
                        all_links = article.find_all('a', href=True)
                        logger.debug(f"Method 3 - Found {len(all_links)} links in the article")

                        for a_tag in all_links:
                            if title.lower() in a_tag.get_text().lower():
                                link = a_tag['href']
                                logger.debug(f"Method 3 - URL from link with matching title text: {link}")
                                break

                        # If still no link, just get the first link in the article
                        if not link and all_links:
                            first_link = all_links[0]
                            link = first_link['href']
                            logger.debug(f"Method 3 - URL from first link in article: {link}")

                    # Method 4: For TechCrunch specifically, try to extract from the article class
                    if not link and 'techcrunch.com' in url:
                        logger.debug("Method 4 - Trying TechCrunch specific extraction")
                        # TechCrunch articles often have the post-block__title class
                        title_block = article.find(class_='post-block__title')
                        if title_block:
                            link_in_title = title_block.find('a', href=True)
                            if link_in_title:
                                link = link_in_title['href']
                                logger.debug(f"Method 4 - URL from TechCrunch title block: {link}")

                        # Try another TechCrunch pattern
                        if not link:
                            # Try to find the article link from the article header
                            header = article.find(class_='post-block__header')
                            if header:
                                link_in_header = header.find('a', href=True)
                                if link_in_header:
                                    link = link_in_header['href']
                                    logger.debug(f"Method 4 - URL from TechCrunch header: {link}")

                    if not link:
                        logger.debug(f"Failed to extract URL for article: {title}")

                    # Make sure the link is absolute
                    if link and not link.startswith(('http://', 'https://')):
                        link = urljoin(base_domain, link)

                    # Extract publication date
                    pub_date = self._extract_date(article, parser_config)

                    # Format the date for display
                    formatted_date = pub_date.isoformat() if pub_date else None

                    # Only add articles with valid titles
                    if title != "No title found":
                        articles.append({
                            'title': title,
                            'url': link,
                            'source_url': url,
                            'pub_date': formatted_date,
                            'timestamp': pub_date  # Keep the datetime object for sorting
                        })
                except Exception as e:
                    logger.warning(f"Error parsing article: {e}")

            return articles
        except Exception as e:
            logger.error(f"Error parsing HTML from {url}: {e}")
            return []

    def scrape_websites(self, urls, parser_configs=None):
        """
        Scrape multiple websites for articles.

        Args:
            urls (list): List of URLs to scrape
            parser_configs (dict, optional): Dictionary mapping URLs to parser configurations

        Returns:
            list: A list of dictionaries containing article information
        """
        if parser_configs is None:
            parser_configs = {}

        all_articles = []

        for url in urls:
            try:
                response = self.fetch_page(url)
                if response:
                    config = parser_configs.get(url)
                    articles = self.parse_articles(response, url, config)
                    all_articles.extend(articles)
                    logger.info(f"Found {len(articles)} articles on {url}")
            except Exception as e:
                logger.error(f"Error scraping {url}: {e}")

        # Filter articles by publication date
        filtered_articles = []
        for article in all_articles:
            timestamp = article.get('timestamp')
            if self._is_within_time_window(timestamp):
                filtered_articles.append(article)

        # Sort articles by publication date (most recent first)
        # First, normalize all timestamps to naive datetimes for comparison
        for article in filtered_articles:
            timestamp = article.get('timestamp')
            if timestamp and timestamp.tzinfo is not None:
                article['timestamp'] = timestamp.replace(tzinfo=None)

        # Now sort with normalized timestamps
        sorted_articles = sorted(
            filtered_articles,
            key=lambda x: (x.get('timestamp') is not None, x.get('timestamp') or datetime.datetime.min),
            reverse=True
        )

        # Remove the timestamp field from the sorted articles
        for article in sorted_articles:
            article.pop('timestamp', None)

        logger.info(f"Filtered from {len(all_articles)} to {len(sorted_articles)} articles within the time window")

        return sorted_articles

    def save_to_csv(self, articles, filename="articles.csv"):
        """
        Save the articles to a CSV file.

        Args:
            articles (list): List of article dictionaries
            filename (str): Name of the CSV file

        Returns:
            bool: True if successful, False otherwise
        """
        if not articles:
            logger.warning("No articles to save")
            return False

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = articles[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(articles)
            logger.info(f"Saved {len(articles)} articles to {filename}")
            return True
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return False

    def save_to_dataframe(self, articles):
        """
        Convert the articles to a pandas DataFrame.

        Args:
            articles (list): List of article dictionaries

        Returns:
            pandas.DataFrame: DataFrame containing the articles
        """
        if not articles:
            logger.warning("No articles to convert to DataFrame")
            return pd.DataFrame()

        return pd.DataFrame(articles)

    def save_to_json(self, articles, filename="articles.json"):
        """
        Save the articles to a JSON file.

        Args:
            articles (list): List of article dictionaries
            filename (str): Name of the JSON file

        Returns:
            bool: True if successful, False otherwise
        """
        if not articles:
            logger.warning("No articles to save to JSON")
            return False

        try:
            # Create a clean version of the articles for JSON output
            json_articles = []
            for article in articles:
                # Create a new dictionary with the desired fields
                json_article = {
                    'title': article.get('title', ''),
                    'url': article.get('url', ''),
                    'pub_date': article.get('pub_date', ''),
                    'source_website': article.get('source_url', '')
                }
                json_articles.append(json_article)

            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(json_articles, jsonfile, indent=4, ensure_ascii=False)

            logger.info(f"Saved {len(articles)} articles to {filename}")
            return True
        except Exception as e:
            logger.error(f"Error saving to JSON: {e}")
            return False

    def filter_and_save_to_json(self, articles, filename="filtered_tech_articles.json", strict=False):
        """
        Filter articles based on technical relevance and save to a JSON file.

        Args:
            articles (list): List of article dictionaries
            filename (str): Name of the JSON file
            strict (bool): If True, requires multiple include keywords to match

        Returns:
            tuple: (bool, list) - Success status and list of filtered articles
        """
        if not articles:
            logger.warning("No articles to filter and save")
            return False, []

        try:
            # Filter articles based on relevance
            filtered_articles = []
            for article in articles:
                if is_relevant_article(article, strict):
                    filtered_articles.append(article)

            # Create a clean version of the filtered articles for JSON output
            json_articles = []
            for article in filtered_articles:
                # Create a new dictionary with the desired fields
                json_article = {
                    'title': article.get('title', ''),
                    'url': article.get('url', ''),
                    'pub_date': article.get('pub_date', ''),
                    'source_website': article.get('source_url', '')
                }
                json_articles.append(json_article)

            # Always overwrite the file with the new filtered results
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(json_articles, jsonfile, indent=4, ensure_ascii=False)

            logger.info(f"Filtered from {len(articles)} to {len(filtered_articles)} articles")
            logger.info(f"Saved {len(filtered_articles)} filtered articles to {filename}")
            return True, filtered_articles
        except Exception as e:
            logger.error(f"Error filtering and saving to JSON: {e}")
            return False, []

def main():
    """
    Main function to run the web scraper.
    """
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Web scraper for tech news articles')
    parser.add_argument('--days', type=int, default=2, help='Number of days to look back for articles (default: 2)')
    parser.add_argument('--filter', action='store_true', help='Filter articles for technical relevance')
    parser.add_argument('--strict', action='store_true', help='Use strict filtering (requires multiple technical keywords)')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    args = parser.parse_args()

    # Set logging level based on debug flag
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Tech news URLs to scrape
    urls = [
        "https://news.ycombinator.com/",
        "https://www.theverge.com/",
        "https://techcrunch.com/category/artificial-intelligence/",
        "https://analyticsindiamag.com/deep-tech/",
        "https://scitechdaily.com/news/technology/"
    ]

    # Custom parser configurations for each website
    parser_configs = {
        "https://news.ycombinator.com/": {
            'article_selector': '.athing',
            'title_selector': '.title a',
            'link_selector': '.title a',
            'link_attribute': 'href',
            'date_selector': '.age a'
        },
        "https://www.theverge.com/": {
            'article_selector': '.duet--content-cards--content-card',
            'title_selector': 'h2 a, h3 a',
            'link_selector': 'h2 a, h3 a, a.group',  # Added a.group which is used in some Verge layouts
            'link_attribute': 'href',
            'date_selector': 'time'
        },
        "https://techcrunch.com/category/artificial-intelligence/": {
            'article_selector': 'div.post-block',  # More specific selector
            'title_selector': 'h2.post-block__title a, h3.post-block__title a',  # More specific title selector
            'link_selector': '.post-block__title a, article.post-block a.post-block__title__link',  # Multiple link patterns
            'link_attribute': 'href',
            'date_selector': 'time, .river-byline time'
        },
        "https://analyticsindiamag.com/deep-tech/": {
            'article_selector': 'article.jeg_post',  # More specific selector
            'title_selector': 'h3.jeg_post_title a, h2.jeg_post_title a',
            'link_selector': '.jeg_post_title a, .jeg_thumb a',  # Added thumbnail links as fallback
            'link_attribute': 'href',
            'date_selector': '.jeg_meta_date'
        },
        "https://scitechdaily.com/news/technology/": {
            'article_selector': 'article.entry, .entry',
            'title_selector': 'h2.entry-title a, h1.entry-title a',
            'link_selector': '.entry-title a, .entry-thumbnail a',  # Added thumbnail links
            'link_attribute': 'href',
            'date_selector': '.meta-date, time.entry-date, .entry-date'
        }
    }

    # Create a scraper with the specified time window
    time_window_days = args.days
    print(f"Scraping articles from the last {time_window_days} days...")

    scraper = WebScraper(
        delay_min=2,
        delay_max=5,
        time_window_days=time_window_days,
        respect_robots=False  # Set to False for testing purposes
    )

    # Scrape the websites
    articles = scraper.scrape_websites(urls, parser_configs)

    # Save to CSV
    csv_filename = f"articles_{time_window_days}_days.csv"
    scraper.save_to_csv(articles, csv_filename)

    # Save to JSON
    json_filename = f"articles_{time_window_days}_days.json"
    scraper.save_to_json(articles, json_filename)

    # Filter articles if requested
    if args.filter:
        # Use different filenames for normal and strict filtering
        if args.strict:
            filtered_filename = "filtered_tech_articles_strict.json"
        else:
            filtered_filename = "filtered_tech_articles.json"

        success, filtered_articles = scraper.filter_and_save_to_json(
            articles,
            filename=filtered_filename,
            strict=args.strict
        )

        if success:
            # Convert filtered articles to DataFrame for display
            filtered_df = scraper.save_to_dataframe(filtered_articles)

            # Print filtering summary
            strictness = "strict" if args.strict else "normal"
            print(f"\nFiltered articles using {strictness} mode:")
            print(f"Found {len(filtered_articles)} technically relevant articles out of {len(articles)} total")
            print(f"Filtered results saved to {filtered_filename}")

            # Display the first few filtered articles
            if not filtered_df.empty:
                print("\nFirst 5 filtered articles:")
                for i, row in filtered_df.head().iterrows():
                    print(f"{i+1}. {row['title']} - {row['pub_date'] or 'No date'}")
                    print(f"   Source: {row['source_url']}")
                    print(f"   URL: {row['url']}")
                    print()

    # Convert to DataFrame for display
    df = scraper.save_to_dataframe(articles)

    # Print summary
    print(f"\nScraped {len(articles)} articles from the last {time_window_days} days")
    print(f"Results saved to {csv_filename} and {json_filename}")

    # Display the first few articles if not already showing filtered results
    if not args.filter and not df.empty:
        print("\nFirst 5 articles:")
        for i, row in df.head().iterrows():
            print(f"{i+1}. {row['title']} - {row['pub_date'] or 'No date'}")
            print(f"   Source: {row['source_url']}")
            print(f"   URL: {row['url']}")
            print()

if __name__ == "__main__":
    main()